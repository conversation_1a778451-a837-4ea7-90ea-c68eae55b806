"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { VoiceSessionAPI } from "@/lib/voice-session-api"
import { toast } from "sonner"

// Add global test functions for debugging
if (typeof window !== 'undefined') {
  (window as any).testConversationGet = async (conversationId: string) => {
    try {
      console.log('Testing conversation get:', { conversationId })
      const result = await VoiceSessionAPI.getConversation(conversationId)
      console.log('Get test result:', result)
      return result
    } catch (error) {
      console.error('Get test error:', error)
      throw error
    }
  }

  (window as any).testConversationUpdate = async (conversationId: string, newTitle: string) => {
    try {
      console.log('Testing conversation update:', { conversationId, newTitle })
      const result = await VoiceSessionAPI.updateConversation(conversationId, { title: newTitle })
      console.log('Update test result:', result)
      return result
    } catch (error) {
      console.error('Update test error:', error)
      throw error
    }
  }
}

interface ConversationMenuProps {
  conversationId: string
  conversationTitle: string
  onConversationUpdated: (id: string, newTitle: string) => void
  onConversationDeleted: (id: string) => void
  isActive?: boolean
}

export default function ConversationMenu({
  conversationId,
  conversationTitle,
  onConversationUpdated,
  onConversationDeleted,
  isActive = false
}: ConversationMenuProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showRenameDialog, setShowRenameDialog] = useState(false)
  const [newTitle, setNewTitle] = useState(conversationTitle)
  const [isLoading, setIsLoading] = useState(false)

  const handleRename = async () => {
    if (!newTitle.trim()) {
      toast.error("Conversation name cannot be empty")
      return
    }

    if (newTitle.trim() === conversationTitle) {
      setShowRenameDialog(false)
      return
    }

    setIsLoading(true)
    try {
      console.log("Attempting to rename conversation:", {
        conversationId,
        oldTitle: conversationTitle,
        newTitle: newTitle.trim()
      })

      // First, try to get the conversation to verify it exists and we have access
      console.log("Verifying conversation exists...")
      const conversation = await VoiceSessionAPI.getConversation(conversationId)
      console.log("Conversation found:", conversation)

      const result = await VoiceSessionAPI.updateConversation(conversationId, {
        title: newTitle.trim()
      })

      console.log("Rename successful:", result)
      onConversationUpdated(conversationId, newTitle.trim())
      setShowRenameDialog(false)
      toast.success("Conversation renamed successfully")
    } catch (error) {
      console.error("Failed to rename conversation:", error)

      // Show more specific error message if available
      let errorMessage = "Failed to rename conversation. Please try again."
      if (error instanceof Error) {
        errorMessage = error.message

        // If it's a VoiceSessionError, try to get more details
        if ('details' in error) {
          console.error("Error details:", (error as any).details)
        }
      }

      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    setIsLoading(true)
    try {
      await VoiceSessionAPI.deleteConversation(conversationId)
      
      onConversationDeleted(conversationId)
      setShowDeleteDialog(false)
      toast.success("Conversation deleted successfully")
    } catch (error) {
      console.error("Failed to delete conversation:", error)
      toast.error("Failed to delete conversation. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleMenuItemClick = (action: 'rename' | 'delete', event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    if (action === 'rename') {
      setNewTitle(conversationTitle)
      setShowRenameDialog(true)
    } else {
      setShowDeleteDialog(true)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
            }}
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem
            onClick={(e) => handleMenuItemClick('rename', e)}
            className="cursor-pointer"
          >
            <Edit className="mr-2 h-4 w-4" />
            Rename conversation
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={(e) => handleMenuItemClick('delete', e)}
            className="cursor-pointer text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete conversation
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Rename Dialog */}
      <Dialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Rename Conversation</DialogTitle>
            <DialogDescription>
              Enter a new name for this conversation.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={newTitle}
                onChange={(e) => setNewTitle(e.target.value)}
                className="col-span-3"
                placeholder="Enter conversation name"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleRename()
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowRenameDialog(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleRename}
              disabled={isLoading || !newTitle.trim()}
            >
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Conversation</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{conversationTitle}"? This action cannot be undone.
              {isActive && (
                <span className="block mt-2 text-amber-600 font-medium">
                  This is your currently active conversation.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
